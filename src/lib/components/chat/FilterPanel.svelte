<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { Button, Checkbox, Datepicker } from 'flowbite-svelte';
	import { CalendarMonthOutline, CloseOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let isOpen = false;
	export let sortedIdentities = [];
	export let unreadCounts = new Map();
	export let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		statuses: new Set(['All']),
		tags: new Set(['All']),
		searchText: ''
	};

	const dispatch = createEventDispatcher();

	// Filter options
	const dateRangeOptions = [
		{ id: 'today', label: 'Today' },
		{ id: 'week', label: 'This Week' },
		{ id: 'month', label: 'This Month' },
		{ id: 'custom', label: 'Custom Range' }
	];

	const unreadFilterOptions = [
		{ id: 'All', label: 'All Messages' },
		{ id: 'unread', label: 'Unread Messages' }
	];

	// Status filter options
	const statusFilterOptions = [
		{ id: 'All', label: 'All Status' },
		{ id: 'open', label: 'Open' },
		{ id: 'assigned', label: 'Assigned' },
		{ id: 'pending-to-close', label: 'Pending to Close' },
		{ id: 'closed', label: 'Closed' }
	];

	// Tag filter options
	const tagFilterOptions = [
		{ id: 'All', label: 'All Tags' },
		{ id: 'vip', label: 'VIP' },
		{ id: 'vvip', label: 'VVIP' }
	];

	// Combined platform+channel filter options
	$: combinedPlatformChannelOptions = [
		{
			id: 'All',
			label: 'All Channels',
			platform: 'All',
			channel: 'All',
			color: 'bg-gray-100 text-gray-700'
		},
		...sortedIdentities
			.map((identity) => {
				const platform = identity.platform;
				const channel = identity.channel_name || 'No Channel';
				const combinedId = `${platform}|${channel}`;
				const platformColor =
					platform === 'LINE'
						? 'bg-green-100 text-green-700'
						: platform === 'WHATSAPP'
							? 'bg-green-100 text-green-700'
							: platform === 'FACEBOOK'
								? 'bg-blue-100 text-blue-700'
								: platform === 'INSTAGRAM'
									? 'bg-purple-100 text-purple-700'
									: platform === 'TELEGRAM'
										? 'bg-sky-100 text-sky-700'
										: 'bg-gray-100 text-gray-700';

				return {
					id: combinedId,
					label: channel,
					platform: platform,
					channel: channel,
					color: platformColor
				};
			})
			.filter(
				(option, index, self) =>
					// Remove duplicates based on combined id
					index === self.findIndex((o) => o.id === option.id)
			)
	];

	let filterPanelElement: HTMLElement;

	// Local state for form inputs
	let selectedDateRange = filterData.dateRange;
	let customStartDate = filterData.customStartDate;
	let customEndDate = filterData.customEndDate;
	let selectedPlatforms = new Set(filterData.platforms);
	let selectedChannels = new Set(filterData.channels);
	let selectedUnreadFilter = new Set(filterData.unreadFilter);
	let selectedStatuses = new Set(filterData.statuses);
	let selectedTags = new Set(filterData.tags);
	let selectedCombinedPlatformChannel = new Set(['All']); // New combined filter state

	$: showCustomDateInputs = selectedDateRange === 'custom';

	// Handle click outside to close
	const handleClickOutside = (event: MouseEvent) => {
		console.log('Click outside detected, isOpen:', isOpen);
		if (isOpen && filterPanelElement && !filterPanelElement.contains(event.target as Node)) {
			// Also check if the click is on the filter button itself (to prevent immediate closing)
			const target = event.target as Element;
			const filterButton = target.closest('[aria-label="Filter conversations"]');
			console.log('Filter button found:', !!filterButton);
			if (!filterButton) {
				console.log('Closing panel due to click outside');
				closePanel();
			}
		}
	};

	// Handle escape key to close
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && isOpen) {
			closePanel();
		}
	};

	// Track if event listeners are attached
	let listenersAttached = false;

	// Reactive statement to manage event listeners based on isOpen state
	$: if (typeof document !== 'undefined') {
		if (isOpen && !listenersAttached) {
			// Add a small delay to prevent immediate closing from the same click that opened the panel
			setTimeout(() => {
				console.log('Adding event listeners');
				document.addEventListener('click', handleClickOutside);
				document.addEventListener('keydown', handleKeydown);
				listenersAttached = true;
			}, 50);
		} else if (!isOpen && listenersAttached) {
			console.log('Removing event listeners');
			document.removeEventListener('click', handleClickOutside);
			document.removeEventListener('keydown', handleKeydown);
			listenersAttached = false;
		}
	}

	onMount(() => {
		return () => {
			// Cleanup on component destroy
			if (typeof document !== 'undefined') {
				document.removeEventListener('click', handleClickOutside);
				document.removeEventListener('keydown', handleKeydown);
			}
		};
	});

	function closePanel() {
		isOpen = false;
		dispatch('close');
	}

	function clearAllFilters() {
		selectedDateRange = null;
		customStartDate = null;
		customEndDate = null;
		selectedPlatforms = new Set(['All']);
		selectedChannels = new Set(['All']);
		selectedUnreadFilter = new Set(['All']);
		selectedStatuses = new Set(['All']);
		selectedTags = new Set(['All']);
		selectedCombinedPlatformChannel = new Set(['All']);
		applyFilters();
	}

	function applyFilters() {
		// Convert combined filter back to separate platform and channel filters for compatibility
		const platforms = new Set();
		const channels = new Set();

		if (selectedCombinedPlatformChannel.has('All')) {
			platforms.add('All');
			channels.add('All');
		} else {
			selectedCombinedPlatformChannel.forEach((combinedId) => {
				if (combinedId !== 'All') {
					const [platform, channel] = combinedId.split('|');
					platforms.add(platform);
					channels.add(channel);
				}
			});

			// If no specific selections, default to All
			if (platforms.size === 0) platforms.add('All');
			if (channels.size === 0) channels.add('All');
		}

		const updatedFilterData = {
			dateRange: selectedDateRange,
			customStartDate,
			customEndDate,
			platforms: platforms,
			channels: channels,
			unreadFilter: selectedUnreadFilter,
			statuses: selectedStatuses,
			tags: selectedTags,
			searchText: '' // Remove search functionality
		};

		dispatch('apply', updatedFilterData);
	}

	function toggleUnreadFilter(filterId: string) {
		if (filterId === 'All') {
			selectedUnreadFilter = new Set(['All']);
		} else {
			selectedUnreadFilter.delete('All');
			if (selectedUnreadFilter.has(filterId)) {
				selectedUnreadFilter.delete(filterId);
			} else {
				selectedUnreadFilter.add(filterId);
			}

			// If no filters selected, select All
			if (selectedUnreadFilter.size === 0) {
				selectedUnreadFilter.add('All');
			}
		}
		selectedUnreadFilter = selectedUnreadFilter; // Trigger reactivity
	}

	function toggleCombinedPlatformChannel(combinedId: string) {
		if (combinedId === 'All') {
			selectedCombinedPlatformChannel = new Set(['All']);
		} else {
			selectedCombinedPlatformChannel.delete('All');
			if (selectedCombinedPlatformChannel.has(combinedId)) {
				selectedCombinedPlatformChannel.delete(combinedId);
			} else {
				selectedCombinedPlatformChannel.add(combinedId);
			}

			// If no filters selected, select All
			if (selectedCombinedPlatformChannel.size === 0) {
				selectedCombinedPlatformChannel.add('All');
			}
		}
		selectedCombinedPlatformChannel = selectedCombinedPlatformChannel; // Trigger reactivity
	}

	function toggleStatusFilter(statusId: string) {
		if (statusId === 'All') {
			selectedStatuses = new Set(['All']);
		} else {
			selectedStatuses.delete('All');
			if (selectedStatuses.has(statusId)) {
				selectedStatuses.delete(statusId);
			} else {
				selectedStatuses.add(statusId);
			}

			// If no filters selected, select All
			if (selectedStatuses.size === 0) {
				selectedStatuses.add('All');
			}
		}
		selectedStatuses = selectedStatuses; // Trigger reactivity
	}

	function toggleTagFilter(tagId: string) {
		if (tagId === 'All') {
			selectedTags = new Set(['All']);
		} else {
			selectedTags.delete('All');
			if (selectedTags.has(tagId)) {
				selectedTags.delete(tagId);
			} else {
				selectedTags.add(tagId);
			}

			// If no filters selected, select All
			if (selectedTags.size === 0) {
				selectedTags.add('All');
			}
		}
		selectedTags = selectedTags; // Trigger reactivity
	}

	// Count active filters
	$: activeFiltersCount = [
		selectedDateRange ? 1 : 0,
		selectedCombinedPlatformChannel.size > 1 || !selectedCombinedPlatformChannel.has('All') ? 1 : 0,
		selectedUnreadFilter.size > 1 || !selectedUnreadFilter.has('All') ? 1 : 0,
		selectedStatuses.size > 1 || !selectedStatuses.has('All') ? 1 : 0,
		selectedTags.size > 1 || !selectedTags.has('All') ? 1 : 0
	].reduce((sum, count) => sum + count, 0);
</script>

{#if isOpen}
	<div
		bind:this={filterPanelElement}
		class="absolute left-0 top-full z-50 mt-2 w-[700px] rounded-lg border border-gray-200 bg-white p-6 shadow-lg"
		role="dialog"
		aria-label="Filter conversations"
		aria-modal="true"
	>
		<!-- Header -->
		<div class="mb-6 flex items-center justify-between">
			<h3 class="text-lg font-semibold text-gray-900">{t('filter')}</h3>
			<div class="flex items-center gap-2">
				<!-- {#if activeFiltersCount > 0}
					<span class="text-sm text-gray-500">
						{activeFiltersCount} active
					</span>
				{/if} -->
				<button
					type="button"
					on:click={closePanel}
					class="rounded-lg p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
					aria-label="Close filter panel"
				>
					<CloseOutline class="h-5 w-5" />
				</button>
			</div>
		</div>

		<!-- Unread Messages Filter - Top Section -->
		<div class="mb-6">
			<!-- <div class="mb-3 text-sm font-medium text-gray-700">Unread Messages</div> -->
			<div class="flex flex-wrap gap-2">
				{#each unreadFilterOptions as filter}
					<div
						class="flex cursor-pointer items-center rounded-lg border border-gray-200 px-3 py-2 transition-colors hover:bg-gray-50 {selectedUnreadFilter.has(
							filter.id
						)
							? 'border-blue-200 bg-blue-50'
							: ''}"
					>
						<Checkbox
							checked={selectedUnreadFilter.has(filter.id)}
							on:change={() => {
								toggleUnreadFilter(filter.id);
								applyFilters();
							}}
							class="mr-2 text-sm text-blue-600 focus:ring-blue-500"
						/>
						<span class="text-sm text-gray-700">{filter.label}</span>
					</div>
				{/each}
			</div>
		</div>

		<!-- Horizontal Filter Row -->
		<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
			<!-- Date Range Filter -->
			<div class="lg:col-span-1">
				<div class="mb-2 text-sm font-medium text-gray-700">
					<!-- <CalendarMonthOutline class="mr-1 inline h-4 w-4" /> -->
					Date Range
				</div>
				<div class="space-y-1">
					{#each dateRangeOptions as option}
						<label class="flex items-center text-sm">
							<input
								type="radio"
								bind:group={selectedDateRange}
								value={option.id}
								on:change={applyFilters}
								class="mr-2 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</label>
					{/each}
				</div>

				{#if showCustomDateInputs}
					<div class="mt-2 space-y-1">
						<div>
							<div class="block text-xs text-gray-600">Start</div>
							<Datepicker
								bind:value={customStartDate}
								placeholder="Start date"
								on:change={applyFilters}
							/>
						</div>
						<div>
							<div class="block text-xs text-gray-600">End</div>
							<Datepicker
								bind:value={customEndDate}
								placeholder="End date"
								on:change={applyFilters}
							/>
						</div>
					</div>
				{/if}
			</div>

			<!-- Combined Platform+Channel Filter -->
			<div class="lg:col-span-1">
				<div class="mb-2 text-sm font-medium text-gray-700">Channels</div>
				<div class="max-h-32 space-y-1 overflow-y-auto">
					{#each combinedPlatformChannelOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedCombinedPlatformChannel.has(option.id)}
								on:change={() => {
									toggleCombinedPlatformChannel(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<div class="flex flex-1 items-center gap-2">
								{#if option.platform !== 'All'}
									<span class="rounded-full px-1.5 py-0.5 text-xs {option.color}">
										{option.platform}
									</span>
								{/if}
								<span class="text-gray-700">{option.label}</span>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Status Filter -->
			<div class="lg:col-span-1">
				<div class="mb-2 text-sm font-medium text-gray-700">Status</div>
				<div class="max-h-50 space-y-1 overflow-y-auto">
					{#each statusFilterOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedStatuses.has(option.id)}
								on:change={() => {
									toggleStatusFilter(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</div>
					{/each}
				</div>
			</div>

			<!-- Tag Filter -->
			<div class="lg:col-span-1">
				<div class="mb-2 text-sm font-medium text-gray-700">Tag</div>
				<div class="max-h-32 space-y-1 overflow-y-auto">
					{#each tagFilterOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedTags.has(option.id)}
								on:change={() => {
									toggleTagFilter(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</div>
					{/each}
				</div>
			</div>
		</div>

		<!-- Actions -->
		<div class="flex justify-between border-t border-gray-200 pt-4">
			<Button
				color="none"
				class="text-gray-600 hover:bg-gray-100"
				on:click={clearAllFilters}
				disabled={activeFiltersCount === 0}
			>
				{t('clear')}
			</Button>
			<Button color="blue" on:click={closePanel}>{t('chat_center_filter_button')}</Button>
		</div>
	</div>
{/if}
